export interface StoreItem {
  NutroStoreID: string;
  StoreName: string;
  Address: string;
  City: string;
  State: string;
  ZIP: string;
  Phone: string;
  Status: string;
  TM: string;
  OpenDate?: string;
  LastUpdated?: string;
}

export interface StoreParams {
  limit?: number;
  page?: number;
  generic_search?: string;
  StoreName?: string;
}
export interface NutroDetailsStoreParams {
  limit?: number;
  page?: number;
  nutrostore_id?: number;
}

export interface StoreState {
  generic_search: string;
  filters: {
    StoreName: string;
    DistStoreID: string;
    DataSource: string;
    City: string;
    State: string;
    ZipCode: string;
    NutroStoreID: string;
    Address: string;
    DistStorePhone: string;
    DistStoreZip: string;
    DistStoreState: string;
    DistStoreCity: string;
    DistStoreAddress: string;
    DistStoreName: string;
  };
  stores: StoreItem[];
  distStores: DistStoreItem[];
  storeDetails: StoreItem | null;
  storeMappings: StoreMappingDetail[];
  storeMetadata: StoreMetadata | null;
  isLoading: boolean;
  error: string | null;
  totalCount: number;
  nutroStoreDetailtotalCount: number;
  distStoresTotalCount: number;
  unmappedStores: UnmappedStoreItem[];
  unmappedStoresTotalCount: number;
  changeLog: ChangeLogEntry[];
  changeLogTotalCount: number;
  isChangeLogModalOpen: boolean;
  storeSuggestions: StoreSuggestion[];
  storeSuggestionsTotalCount: number;
  isCreatingStore: boolean;
  createStoreError: string | null;
  createStoreSuccess: boolean;
  createdStoreId: number | null;
  mappingDetails: MappingDetail[];
  mappingDetailsTotalCount: number;
  isUpdatingMapping: boolean;
  updateMappingError: string | null;
  updateMappingSuccess: boolean;
  isUpdatingMetadata: boolean;
  updateMetadataError: string | null;
  updateMetadataSuccess: boolean;
  updatedMetadataFields: string[];
  storeAttributions: StoreAttribution[];
  storeAttributionTotal: number;
  storeFilterValues: StoreFilterValues;
  isUpdatingStore: boolean;
  updateStoreError: string | null;
  isDeletingStores: boolean;
  deleteStoresError: string | null;
  deleteStoresResult: DeleteResponse | null;
  searchResults: FindStoreResult[];
  searchTotalCount: number;
  isSearching: boolean;
  searchError: string | null;
}
export interface StoreMappingDetail {
  DataSource: string;
  DistStoreID: string;
  DistStoreName: string;
  DistStoreLocation: string;
  LastTransactionDate: string;
  L52POSSales: number;
  L4POSSales: number;
  TotalPOSSales: number;
  MappingStatus: string;
}

export interface StoreMetadata {
  Address: string;
  Phone: string;
  Contact: string;
  Chain: string;
  AccountType: string;
  CategoryLevelOne: string;
  CategoryLevelTwo: string;
  CategoryLevelThree: string;
  Status: string;
  TM: string;
}

export interface NutroStoreDetailResponse {
  stores: StoreMappingDetail[];
  total_count: number;
}

export interface NutroStoreMetaDataResponse {
  stores: StoreMetadata[];
  total_count: number;
}

export interface DistStoreItem {
  DataSource: string;
  StoreID: string;
  StoreDesc: string;
  CityStateZip: string;
  FirstDt: string;
  LastDt: string;
  Last4POS: string;
  Last52POS: number;
  TotalPOSSales: number;
  MappedTo: string;
  IsPending: boolean;
  VCID?: string;
  DistStoreID?: string;
}

export interface DistStoreParams {
  generic_search?: string;
  NutroStoreID?: string;
  DataSource?: string;
  DistStoreID?: string;
  DistStoreName?: string;
  DistStoreAddress?: string;
  DistStoreCity?: string;
  DistStoreState?: string;
  DistStoreZip?: string;
  DistStorePhone?: string;
  page?: number;
  limit?: number;
}

export interface DistStoreResponse {
  stores: DistStoreItem[];
  totalCount: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface UnmappedStoreItem {
  DataSource: string;
  StoreID: string;
  StoreDesc: string;
  CityStateZip: string;
  FirstDt: string;
  LastDt: string;
  Status: string;
}

export interface UnmappedStoreParams {
  generic_search?: string;
  DataSource?: string;
  DistStoreID?: string;
  DistStoreName?: string;
  DistStoreAddress?: string;
  DistStoreCity?: string;
  DistStoreState?: string;
  DistStoreZip?: string;
  DistStorePhone?: string;
  page?: number;
  limit?: number;
}

export interface UnmappedStoreResponse {
  stores: UnmappedStoreItem[];
  totalCount: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrevious: boolean;
}
export interface ChangeLogEntry {
  ChangeDate: string;
  LastModifiedBy: string;
  ChangeType: string;
  OldValue?: string;
  NewValue?: string;
}

export interface ChangeLogParams {
  nutrostore_id: number;
  date?: string;
  page?: number;
  limit?: number;
}

export interface ChangeLogResponse {
  changes: ChangeLogEntry[];
  totalCount: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrevious: boolean;
}
export interface StoreSuggestion {
  SCORE: number | null;
  "NUTRO ID": string | null;
  STORE: string | null;
  ADDRESS: string | null;
  CITY: string | null;
  STATE: string | null;
  ZIP: string | null;
  PHONE: string | null;
  STATUS: string | null;
  "TERRITORY MANAGER": string | null;
}

export interface StoreSuggestionParams {
  VCID: number;
  page?: number;
  limit?: number;
}

export interface StoreSuggestionResponse {
  suggestions: StoreSuggestion[];
  totalCount: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrevious: boolean;
}
export interface StoreCreateRequest {
  // Basic store information
  StoreName?: string;
  ChainName?: string;
  StoreNumber?: string;

  // Location and account details
  LocationType?: string;
  LocationTypeDetail?: string;
  AccountType?: string;
  AccountTypeDetail?: string;
  ReportCategory?: string;
  ReportCategoryDetail?: string;
  StoreStatus?: string;

  // Location address information
  LocationAddress?: string;
  LocationCity?: string;
  LocationState?: string;
  LocationZipCode?: string;
  LocationCountry?: string;

  // Mailing address information
  MailingAddress?: string;
  MailingCity?: string;
  MailingState?: string;
  MailingZipCode?: string;
  MailingCountry?: string;

  // Contact information
  StoreContact?: string;
  Phone?: string;
  Fax?: string;
  EmailAddress?: string;
  WebAddress?: string;

  // Additional store details
  ISODate?: string;
  Size_Override?: string;
  BuyingGroup?: string;
  Distribution_Notes?: string;

  // Territory and call management
  AccountTM?: string;
  IsCallTarget?: boolean;
  Priority?: string;
  CallFrequency?: number;
  WhereToBuyBrands?: string;
  ProgramEnrollment?: string;
  StoreNotes?: string;

  // Demo territory information
  DemoTerritoryDivID?: number;
  DemoMarket?: string;
  IsDemoTarget?: boolean;
  DemoPriority?: string;
  DemoFrequency?: number;
  DemoNotes?: string;

  // Request information
  RequestBy?: string;
  RequestNotes?: string;

  // System identifiers
  VCID?: number;
  NutroStoreID?: number;
}

export interface StoreCreateResponse {
  success: boolean;
  message: string;
  nutroStoreId?: number;
}
export interface MappingDetail {
  DataSource: string;
  DataStoreName: string;
  DataStoreID: string;
  Address: string;
  CustomerType: string;
  Warehouse: string;
  Rep: string;
  Country: string;
  FirstTransDate: string;
  LastTransDate: string;
  Last4WeekPOS: string;
  Last52WeekPOS: string;
  Total2010On: string;
  Phone: string;
  MappingStatus: string;
  IsPending: boolean;
}

export interface MappingDetailsParams {
  mapping_id: number;
  nutrostore_id?: number;
}

export interface MappingDetailsResponse {
  mappings: MappingDetail[];
  totalCount: number;
}

export interface MappingUnmappingMetadataRequest {
  VCID: number;
  NutrostoreID: number;
}

export interface MappingUnmappingUpdateMetadataRequest {
  VCID: number;
  customer_type?: string;
  rep?: string;
  address?: string;
  warehouse?: string;
  country?: string;
  phone?: string;
}

export interface MappingUnmappingUpdateMetadataResponse {
  success: boolean;
  message: string;
  updated_fields: string[];
  VCID: number;
}

export interface StoreAttribution {
  NutroStoreID?: number;
  StoreName?: string;
  LocationAddress?: string;
  LocationCity?: string;
  LocationState?: string;
  LocationZipCode?: string;
  LocationCountry?: string;
  Phone?: string;
  ChainName?: string;
  StoreNumber?: string;
  StoreStatus?: string;
  AccountType?: string;
  ReportCategory?: string;
  ReportCategoryDetail?: string;
  Priority?: string;
  TerritoryManager?: string;
  DistrictManager?: string;
  KAM?: string;
}

export interface StoreListResponse {
  stores: StoreAttribution[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface FilterValues {
  values: string[];
  total: number;
}

export interface StoreFilterValues {
  store_ids: FilterValues;
  store_names: FilterValues;
  chain_names: FilterValues;
  territory_managers: FilterValues;
  district_managers: FilterValues;
  store_numbers: FilterValues;
  account_types: FilterValues;
  report_categories: FilterValues;
}
export interface StoreUpdateRequest {
  StoreName?: string;
  LocationAddress?: string;
  LocationCity?: string;
  LocationState?: string;
  LocationZipCode?: string;
  LocationCountry?: string;
  Phone?: string;
  ChainName?: string;
  StoreNumber?: string;
  StoreStatus?: string;
  AccountType?: string;
  ReportCategory?: string;
  ReportCategoryDetail?: string;
  Priority?: string;
  TerritoryManager?: string;
  DistrictManager?: string;
  KAM?: string;
}

export interface DeleteRequest {
  nutro_store_ids: number[];
}

export interface DeleteResponse {
  message: string;
  deleted_count: number;
  not_found_ids: number[];
}
export interface GetAllStoresTotalParams {
  store_ids?: string;
  store_names?: string;
  chain_names?: string;
  territory_managers?: string;
  district_managers?: string;
  store_numbers?: string;
  account_types?: string;
  report_categories?: string;
  count_only?: boolean;
  limit?: number;
  offset?: number;
}
export interface FindStoreParams {
  NutroStoreID?: number;
  ChainName?: string;
  StoreName?: string;
  StoreNumber?: string;
  Address?: string;
  City?: string;
  State?: string;
  ZipCode?: string;
  Phone?: string;
  page?: number;
  limit?: number;
}

export interface FindStoreResult {
  NutroStoreID: string;
  ChainName: string;
  StoreName: string;
  StoreNumber: string;
  Address: string;
  City: string;
  State: string;
  ZipCode: string;
  Phone: string;
}

export interface FindStoreResponse {
  results: FindStoreResult[];
  total_count: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_previous: boolean;
}
