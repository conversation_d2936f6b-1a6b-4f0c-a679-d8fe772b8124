import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../store";
import {
  getMappingDetails,
  getStoreSuggestions,
  searchDistStores,
  updateStoreMapping,
} from "../../services/action/store.action";
import TopBarLayout from "../TopBarLayout";
import {
  Box,
  Paper,
  Typography,
  Divider,
  Grid,
  Tabs,
  Tab,
  Button,
  CircularProgress,
} from "@mui/material";
import StoreInfoCard from "../../components/store/StoreInfoCard";
import SuggestionsTab from "../../components/store/tabs/SuggestionsTab";
import DistStoreTab from "../../components/store/tabs/DistStoreTab";
import FindStoreTab from "../../components/store/tabs/FindStoreTab";
import NewStoreModal from "../../components/store/tabs/NewStoreModal";
import styles from "../../styles/StoreDescDetails.module.css";
import { useAppDispatch, useAppSelector } from "../../services/hooks/store.hooks";

function TabPanel(props: { children?: React.ReactNode; index: number; value: number }) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}

const StoreDescDetailsPage = () => {
  const location = useLocation();
  const appDispatch = useAppDispatch();
  const [distStoreId, setDistStoreId] = useState<string | null>(null);
  const { storeId } = useParams<{ storeId: string }>();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [value, setValue] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedStore, setSelectedStore] = useState<{ VCID: number; NutrostoreID: number } | null>(
    null,
  );
  const [isPending, setIsPending] = useState(false);
  const [isTabLoading, setIsTabLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  const {
    mappingDetails,
    isLoading: isReduxLoading,
    error,
    storeSuggestions,
    distStores,
    distStoresTotalCount,
    storeSuggestionsTotalCount,
  } = useSelector((state: RootState) => state.store);

  const [distStoresPagination, setDistStoresPagination] = React.useState({
    page: 1,
    rowsPerPage: 10,
  });

  const [suggestionsPagination, setSuggestionsPagination] = React.useState({
    page: 1,
    rowsPerPage: 10,
  });

  useEffect(() => {
    if (location.state?.distStoreId) {
      setDistStoreId(location.state.distStoreId);
    }
  }, [location.state]);

  useEffect(() => {
    if (storeId) {
      const mappingId = parseInt(storeId);
      setIsInitialLoading(true);
      Promise.all([
        appDispatch(getMappingDetails({ mapping_id: mappingId })),
        fetchSuggestions(),
        fetchDistStores(),
      ]).finally(() => setIsInitialLoading(false));
    }
  }, [storeId, dispatch]);

  useEffect(() => {
    if (value === 0) {
      setIsTabLoading(true);
      fetchSuggestions().finally(() => setIsTabLoading(false));
    } else if (value === 1) {
      setIsTabLoading(true);
      fetchDistStores().finally(() => setIsTabLoading(false));
    }
    // No loading for Find Store and New Store tabs as they don't fetch data
  }, [value, suggestionsPagination, distStoresPagination]);

  const fetchSuggestions = () => {
    if (storeId) {
      return appDispatch(
        getStoreSuggestions({
          VCID: parseInt(storeId),
          limit: suggestionsPagination.rowsPerPage,
          page: suggestionsPagination.page,
        }),
      );
    }
    return Promise.resolve();
  };

  const fetchDistStores = () => {
    if (storeId) {
      return appDispatch(
        searchDistStores({
          DistStoreID: distStoreId,
          limit: distStoresPagination.rowsPerPage,
          page: distStoresPagination.page,
        }),
      );
    }
    return Promise.resolve();
  };

  const handleStoreSelect = (store: { VCID: number; NutrostoreID: number }) => {
    setSelectedStore(store);
  };

  const handleSave = async () => {
    if (!selectedStore || !storeId) return;

    setIsPending(true);
    try {
      await appDispatch(
        updateStoreMapping({
          VCID: parseInt(storeId),
          NutrostoreID: selectedStore.NutrostoreID,
        }),
      );

      // Refresh the data after successful mapping
      appDispatch(getMappingDetails({ mapping_id: parseInt(storeId) }));
      fetchSuggestions();
      fetchDistStores();

      // Reset selection
      setSelectedStore(null);
    } catch (error) {
      console.error("Failed to update mapping:", error);
    } finally {
      setIsPending(false);
    }
  };

  const handleUnmap = async () => {
    if (!storeId) return;

    setIsPending(true);
    try {
      await appDispatch(
        updateStoreMapping({
          VCID: parseInt(storeId),
          NutrostoreID: null,
        }),
      );

      // Refresh the data after successful unmapping
      appDispatch(getMappingDetails({ mapping_id: parseInt(storeId) }));
      fetchSuggestions();
      fetchDistStores();

      // Reset selection
      setSelectedStore(null);
    } catch (error) {
      console.error("Failed to unmap:", error);
    } finally {
      setIsPending(false);
    }
  };

  const handleCancel = () => {
    setSelectedStore(null);
  };

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const handleMappedToClick = (nutroId: string) => {
    navigate(`/store-search/${nutroId}`);
  };

  const handleDistStoresPageChange = (newPage: number, newRowsPerPage: number) => {
    setDistStoresPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const handleSuggestionsPageChange = (newPage: number, newRowsPerPage: number) => {
    setSuggestionsPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const storeInfo =
    mappingDetails.length > 0
      ? {
          storeId: storeId || "",
          description: mappingDetails[0].DataStoreName,
          dataSource: mappingDetails[0].DataSource,
          address: mappingDetails[0].Address,
          firstSeen: mappingDetails[0].FirstTransDate,
          lastUpdated: mappingDetails[0].LastTransDate,
          status: mappingDetails[0].MappingStatus,
          phone: mappingDetails[0].Phone,
          rep: mappingDetails[0].Rep,
          customerType: mappingDetails[0].CustomerType,
          warehouse: mappingDetails[0].Warehouse,
          country: mappingDetails[0].Country,
          last4WeekPOS: mappingDetails[0].Last4WeekPOS,
          last52WeekPOS: mappingDetails[0].Last52WeekPOS,
          total2010On: mappingDetails[0].Total2010On,
          isPending: mappingDetails[0].IsPending,
        }
      : null;

  if (isInitialLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!storeInfo) {
    return <div>Store not found</div>;
  }

  return (
    <div className={styles.pageContainer}>
      <TopBarLayout
        breadcrumbItems={["Admin", "Store Description", storeId || ""]}
        onSearchChange={() => {}}
      />

      <Grid container spacing={3} sx={{ p: 3 }}>
        {/* Left Section - Store Info */}
        <Grid item xs={12} md={4}>
          <StoreInfoCard storeInfo={storeInfo} onUnmap={handleUnmap} isPending={isPending} />
        </Grid>

        {/* Right Section - Tabs */}
        <Grid item xs={12} md={8}>
          <Paper elevation={3} className={styles.tabsContainer}>
            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
              }}
            >
              {/* Tabs Header with buttons */}
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  borderBottom: 1,
                  borderColor: "divider",
                  paddingX: 2,
                }}
              >
                <Tabs value={value} onChange={handleChange} aria-label="store tabs">
                  <Tab label="Suggestions" {...a11yProps(0)} />
                  <Tab label="Dist Store ID" {...a11yProps(1)} />
                  <Tab label="Find Store" {...a11yProps(2)} />
                  <Tab label="New Store" {...a11yProps(3)} />
                </Tabs>

                {selectedStore && value !== 3 && (
                  <Box sx={{ display: "flex", gap: 1 }}>
                    <Button
                      variant="outlined"
                      onClick={handleCancel}
                      disabled={isPending}
                      sx={{ minWidth: 100 }}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      onClick={handleSave}
                      disabled={isPending}
                      sx={{ minWidth: 100 }}
                    >
                      {isPending ? "Saving..." : "Save"}
                    </Button>
                  </Box>
                )}
              </Box>

              {/* Tab Panels */}
              <Box>
                <TabPanel value={value} index={0}>
                  {isTabLoading ? (
                    <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <SuggestionsTab
                      storeId={storeId || ""}
                      onMappedToClick={handleMappedToClick}
                      onStoreSelect={handleStoreSelect}
                      selectedStore={selectedStore}
                      suggestions={storeSuggestions}
                      isLoading={isReduxLoading}
                      pagination={suggestionsPagination}
                      onPageChange={handleSuggestionsPageChange}
                      totalCount={storeSuggestionsTotalCount}
                    />
                  )}
                </TabPanel>

                <TabPanel value={value} index={1}>
                  {isTabLoading ? (
                    <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
                      <CircularProgress />
                    </Box>
                  ) : (
                    <DistStoreTab
                      storeId={storeId || ""}
                      onMappedToClick={handleMappedToClick}
                      distStores={distStores}
                      distStoresTotalCount={distStoresTotalCount}
                      isLoading={isReduxLoading}
                      pagination={distStoresPagination}
                      onPageChange={handleDistStoresPageChange}
                    />
                  )}
                </TabPanel>

                <TabPanel value={value} index={2}>
                  <FindStoreTab
                    storeId={storeId || ""}
                    onMappedToClick={handleMappedToClick}
                    onStoreSelect={handleStoreSelect}
                    selectedStore={selectedStore}
                  />
                </TabPanel>

                <TabPanel value={value} index={3}>
                  <Box className={styles.newStoreTab}>
                    <Button variant="contained" size="large" onClick={() => setIsModalOpen(true)}>
                      Create New Store
                    </Button>
                  </Box>
                </TabPanel>
              </Box>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      <NewStoreModal
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        storeInfo={{
          address: storeInfo.address,
          dataSource: storeInfo.dataSource,
          country: storeInfo.country,
          phone: storeInfo.phone,
          rep: storeInfo.rep,
          customerType: storeInfo.customerType,
          warehouse: storeInfo.warehouse,
        }}
      />
    </div>
  );
};

export default StoreDescDetailsPage;
