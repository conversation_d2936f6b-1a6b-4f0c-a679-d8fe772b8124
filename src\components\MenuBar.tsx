import React from "react";
import MenuItemWithDropdown from "./MenuItemWithDropdown";
import { Button } from "@mars/vizx-react";
import { useNavigate } from "react-router-dom";

const MenuBar: React.FC<{ selectedMenuItem: string }> = ({ selectedMenuItem }) => {
  const navigate = useNavigate();
  const menuItems = [
    { id: "/", title: "Home" },
  ];

  const adminMenuItems = [
    { id: "/attribute-list", title: "Attribute List" },
    { id: "/user-list", title: "User List" },
    { id: "/new-user", title: "New User" },
  ];

  const storeMenuItems = [
    { id: "/store-search", title: "Store Search" },
    { id: "/mapped-store-search", title: "Mapped Search" },
    { id: "/unmapped-store-search", title: "Unmapped Search" },
    { id: "/store-attribute", title: "Store Attribute" },
  ];

  const productMenuItems = [
    { id: "/product-search", title: "Product Search" },
    { id: "/product-attribute", title: "Product Attribute" },
  ];

  return (
    <div
      className="flex justify-center gap-10 text-white text-sm font-medium"
      style={{ backgroundColor: "rgba(255, 20, 20, 1)" }}
    >
      {menuItems.map((item) => (
        <Button
          key={item.id}
          variant="text"
          onClick={() => navigate(item.id)}
          style={{
            color: selectedMenuItem === item.id ? "white" : "#fff",
            borderBottom: selectedMenuItem === item.id ? "2px solid white" : "none",
            padding: "0 50px", 
          }}
        >
          {item.title}
        </Button>
      ))}
      <MenuItemWithDropdown
        label="Store"
        menuItems={storeMenuItems}
        selectedMenuItem={selectedMenuItem}
      />
      <MenuItemWithDropdown
        label="Product"
        menuItems={productMenuItems}
        selectedMenuItem={selectedMenuItem}
      />
      <MenuItemWithDropdown
        label="Admin"
        menuItems={adminMenuItems}
        selectedMenuItem={selectedMenuItem}
      />
    </div>
  );
};

export default MenuBar;