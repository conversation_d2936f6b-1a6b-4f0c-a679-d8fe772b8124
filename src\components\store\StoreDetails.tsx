import React, { useEffect, useState } from "react";
import { useParams, useLocation } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../services/hooks/store.hooks";
import TopBarLayout from "../TopBarLayout";
import { Box, Paper, Typography, Divider, Grid, Button, Link } from "@mui/material";
import TableComponent from "../TableComponent";
import { getNutroStoreDetail, getNutroStoreMetadata } from "../../services/action/store.action";
import ChangeLogModal from "./ChangeLogModal";
import { openChangeLogModal, closeChangeLogModal } from "../../store/slice/storeSlice";
import { useNavigate } from "react-router-dom";
import { DistStoreItem } from "../../types/store.types";

const StoreDetailsPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [storeName, setStoreName] = useState<string | null>(null);
  const { nutroStoreID } = useParams<{ nutroStoreID: string }>();
  const dispatch = useAppDispatch();
  const {
    storeMappings,
    storeMetadata,
    isLoading,
    error,
    isChangeLogModalOpen,
    nutroStoreDetailtotalCount,
  } = useAppSelector((state) => state.store);
  const [pagination, setPagination] = useState({
    page: 1,
    rowsPerPage: 10,
  });
  const [nutroId, setNutroId] = useState<number | null>(null);
  useEffect(() => {
    if (location.state?.storeName) {
      setStoreName(location.state.storeName);
    }
  }, [location.state]);
  useEffect(() => {
    if (nutroStoreID) {
      const id = parseInt(nutroStoreID);
      setNutroId(id);
      dispatch(
        getNutroStoreDetail({
          nutrostore_id: id,
          limit: pagination.rowsPerPage,
          page: pagination.page,
        }),
      );
      dispatch(getNutroStoreMetadata(id));
    }
  }, [nutroStoreID, dispatch, pagination.page, pagination.rowsPerPage]);

  const handleOpenChangeLog = () => {
    dispatch(openChangeLogModal());
  };

  const handleCloseChangeLog = () => {
    dispatch(closeChangeLogModal());
  };

  const handleSearch = (query: string) => {
    // Handle search if needed
  };

  const handlePageChange = (newPage: number, newRowsPerPage: number) => {
    setPagination({
      page: newPage,
      rowsPerPage: newRowsPerPage,
    });
  };

  const handleStoreNameClick = (row: DistStoreItem) => {
    if (!row?.VCID) {
      console.error("VCID is missing in row data");
      return;
    }
    console.log("");
    navigate(`/store-desc/${row.VCID}`, {
      state: {
        distStoreId: row.DistStoreID, // Or whatever field contains the store name
      },
    });
  };

  const formatValue = (value: any) => {
    if (value === null || value === undefined || value === "") {
      return "-";
    }
    return value;
  };

  const mappingColumns = [
    {
      id: "DataSource",
      label: "Data Source",
      description: "External system name",
      format: (value: string) => formatValue(value),
    },
    {
      id: "DistStoreID",
      label: "Store ID",
      description: "ID in external system",
      format: (value: string) => formatValue(value),
    },
    {
      id: "DistStoreName",
      label: "Store Name",
      description: "Name in external system",
      format: (value: string, row: DistStoreItem) =>
        value ? (
          <Link
            href="#"
            onClick={() => handleStoreNameClick(row)}
            sx={{
              color: "primary.main",
              textDecoration: "underline",
              cursor: "pointer",
              "&:hover": {
                color: "primary.dark",
                fontWeight: "500",
              },
            }}
          >
            {value}
          </Link>
        ) : (
          "-"
        ),
    },
    {
      id: "DistStoreLocation",
      label: "Location",
      description: "City, State ZIP",
      format: (value: string) => formatValue(value),
    },
    {
      id: "LastTransactionDate",
      label: "Last Transaction",
      description: "Last transaction date",
      format: (value: string) => formatValue(value),
    },
    {
      id: "L52POSSales",
      label: "Last 52 Weeks Sales",
      description: "Sales over last 52 weeks",
      format: (value: string) => formatValue(value),
    },
    {
      id: "L4POSSales",
      label: "Last 4 Weeks Sales",
      description: "Sales over last 4 weeks",
      format: (value: string) => formatValue(value),
    },
    {
      id: "TotalPOSSales",
      label: "Total Sales",
      description: "Total sales recorded",
      format: (value: string) => formatValue(value),
    },
    {
      id: "MappingStatus",
      label: "Status",
      description: "Mapping status",
      format: (value: string) =>
        value ? (
          <Box
            sx={{
              color: value === "Active" ? "success.main" : "error.main",
              fontWeight: "bold",
            }}
          >
            {value}
          </Box>
        ) : (
          "-"
        ),
    },
    // {
    //   id: "VCID",
    //   label: "VCID",
    //   description: "Vendor Customer ID",
    //   format: (value: number) => formatValue(value),
    // },
  ];

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!storeMetadata && !isLoading) {
    return (
      <Box sx={{ p: 3, textAlign: "center" }}>
        <Typography variant="h6">No data found for this store</Typography>
      </Box>
    );
  }

  const hasMappings = storeMappings && storeMappings.length > 0;

  return (
    <div>
      <TopBarLayout
        breadcrumbItems={["Admin", "Store Search", nutroStoreID || ""]}
        onSearchChange={handleSearch}
      />

      <Grid container spacing={3} sx={{ p: 3 }}>
        {/* Left Section - Store Details */}
        <Grid item xs={12} md={4}>
          <Paper elevation={3} sx={{ p: 3, height: "100%" }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 3,
              }}
            >
              <Typography variant="subtitle1" sx={{ fontWeight: "bold", fontSize: "1.1rem" }}>
                {nutroStoreID} - {formatValue(storeName || " ")}
              </Typography>
              {/* <Button
                variant="outlined"
                onClick={handleOpenChangeLog}
                sx={{
                  textTransform: "none",
                  fontWeight: "bold",
                }}
              >
                View History
              </Button> */}
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                Address:{" "}
                <span style={{ fontWeight: "normal" }}>{formatValue(storeMetadata?.Address)}</span>
              </Typography>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Box sx={{ mb: 2 }}>
              <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                Phone:{" "}
                <span style={{ fontWeight: "normal" }}>{formatValue(storeMetadata?.Phone)}</span>
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "bold", mt: 1 }}>
                Contact:{" "}
                <span style={{ fontWeight: "normal" }}>{formatValue(storeMetadata?.Contact)}</span>
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "bold", mt: 1 }}>
                TM: <span style={{ fontWeight: "normal" }}>{formatValue(storeMetadata?.TM)}</span>
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                Status:
                <span
                  style={{
                    color: (storeMetadata?.Status || "Active") === "Active" ? "#4CAF50" : "#F44336",
                    fontWeight: "bold",
                    marginLeft: "4px",
                  }}
                >
                  {formatValue(storeMetadata?.Status)}
                </span>
              </Typography>
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                Chain:{" "}
                <span style={{ fontWeight: "normal" }}>{formatValue(storeMetadata?.Chain)}</span>
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "bold", mt: 1 }}>
                Account Type:{" "}
                <span style={{ fontWeight: "normal" }}>
                  {formatValue(storeMetadata?.AccountType)}
                </span>
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "bold", mt: 1 }}>
                Category Level 1:{" "}
                <span style={{ fontWeight: "normal" }}>
                  {formatValue(storeMetadata?.CategoryLevelOne)}
                </span>
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "bold", mt: 1 }}>
                Category Level 2:{" "}
                <span style={{ fontWeight: "normal" }}>
                  {formatValue(storeMetadata?.CategoryLevelTwo)}
                </span>
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "bold", mt: 1 }}>
                Category Level 3:{" "}
                <span style={{ fontWeight: "normal" }}>
                  {formatValue(storeMetadata?.CategoryLevelThree)}
                </span>
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Right Section - Store Mappings */}
        <Grid item xs={12} md={8}>
          <Paper elevation={3} sx={{ p: 3, height: "100%" }}>
            <Typography variant="subtitle1" sx={{ fontWeight: "bold", mb: 1 }}>
              Store Mappings
            </Typography>
            <Divider sx={{ my: 1 }} />

            {hasMappings ? (
              <TableComponent
                columns={mappingColumns}
                rows={storeMappings}
                showActions={false}
                isLoading={isLoading}
                rowsPerPage={pagination.rowsPerPage}
                page={pagination.page}
                totalCount={nutroStoreDetailtotalCount}
                onPageChange={handlePageChange}
              />
            ) : (
              <Box sx={{ p: 3, textAlign: "center" }}>
                <Typography variant="body1">No store mappings found</Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>

      {nutroId && (
        <ChangeLogModal
          nutroStoreID={nutroId}
          open={isChangeLogModalOpen}
          onClose={handleCloseChangeLog}
        />
      )}
    </div>
  );
};

export default StoreDetailsPage;
