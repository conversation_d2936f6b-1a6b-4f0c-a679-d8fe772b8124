import client from "../../axiosConfig";
import {
  ChangeLogParams,
  DeleteRequest,
  DeleteResponse,
  DistStoreParams,
  FindStoreParams,
  FindStoreResponse,
  GetAllStoresTotalParams,
  MappingDetailsParams,
  MappingUnmappingMetadataRequest,
  MappingUnmappingUpdateMetadataRequest,
  MappingUnmappingUpdateMetadataResponse,
  NutroDetailsStoreParams,
  StoreAttribution,
  StoreCreateRequest,
  StoreFilterValues,
  StoreListResponse,
  StoreParams,
  StoreSuggestionParams,
  StoreUpdateRequest,
  UnmappedStoreParams,
} from "../../types/store.types";

const StoreService = {
  async getSearchStores(params: StoreParams) {
    const response = await client.get("/stores/search/", { params });
    return {
      stores: response.data.stores,
      totalCount: response.data.total_count,
    };
  },
  async getNutroStoreDetail(params: NutroDetailsStoreParams) {
    const newParams = {
      limit: params?.limit,
      page: params?.page,
    };

    const response = await client.get(`/stores/nutro-detail/${params.nutrostore_id}`, {
      params: newParams,
    });
    return {
      stores: response.data.stores,
      totalCount: response.data.total_count,
    };
  },

  async getNutroStoreMetadata(nutrostore_id: number) {
    const response = await client.get(`/stores/nutro-metadata/${nutrostore_id}`);
    return response.data;
  },
  async searchDistStores(params: DistStoreParams) {
    const response = await client.get("/stores/dist-stores-or-mapped-stores/search", {
      params,
    });
    return {
      stores: response.data.stores,
      totalCount: response.data.total_count,
      page: response.data.page,
      limit: response.data.limit,
      hasNext: response.data.has_next,
      hasPrevious: response.data.has_previous,
    };
  },
  async searchUnmappedStores(params: UnmappedStoreParams) {
    const response = await client.get("/stores/unmapped-stores/search", {
      params,
    });
    return {
      stores: response.data.stores,
      totalCount: response.data.total_count,
      page: response.data.page,
      limit: response.data.limit,
      hasNext: response.data.has_next,
      hasPrevious: response.data.has_previous,
    };
  },
  async getChangeLog(params: ChangeLogParams) {
    const response = await client.get("/stores/change-log", { params });
    return {
      changes: response.data.changes,
      totalCount: response.data.total_count,
      page: response.data.page,
      limit: response.data.limit,
      hasNext: response.data.has_next,
      hasPrevious: response.data.has_previous,
    };
  },
  async getStoreSuggestions(params: StoreSuggestionParams) {
    const response = await client.get("/stores/store-description/suggestions", { params });
    return {
      suggestions: response.data.suggestions,
      totalCount: response.data.total_count,
      page: response.data.page,
      limit: response.data.limit,
      hasNext: response.data.has_next,
      hasPrevious: response.data.has_previous,
    };
  },
  async createStore(storeData: StoreCreateRequest) {
    const response = await client.post("/stores/store-description/add", storeData);
    return {
      success: response.data.success,
      message: response.data.message,
      nutroStoreId: response.data.nutro_store_id,
    };
  },
  async getMappingDetails(params: MappingDetailsParams) {
    const response = await client.get("/stores/mapping/details", { params });
    return {
      mappings: response.data.mappings,
      totalCount: response.data.total_count,
    };
  },
  async updateStoreMapping(requestData: MappingUnmappingMetadataRequest) {
    const response = await client.put("/stores/mapping_unmapping/nutrostore", requestData);
    return response.data;
  },

  async updateStoreMappingMetadata(requestData: MappingUnmappingUpdateMetadataRequest) {
    const response = await client.put<MappingUnmappingUpdateMetadataResponse>(
      "/stores/mapping_unmapping/update_metadata",
      requestData,
    );
    return response.data;
  },
  async getAllStores(params: {
    page?: number;
    limit?: number;
    store_ids?: string; // Changed to string for comma-separated values
    store_names?: string;
    chain_names?: string;
    territory_managers?: string;
    district_managers?: string;
    store_numbers?: string;
    account_types?: string;
    report_categories?: string;
  }) {
    // Convert array parameters to comma-separated strings
    const queryParams: Record<string, any> = {
      page: params.page ?? 1,
      limit: params.limit ?? 100,
    };

    // Only include parameters that have values
    if (params.store_ids) queryParams.store_ids = params.store_ids;
    if (params.store_names) queryParams.store_names = params.store_names;
    if (params.chain_names) queryParams.chain_names = params.chain_names;
    if (params.territory_managers) queryParams.territory_managers = params.territory_managers;
    if (params.district_managers) queryParams.district_managers = params.district_managers;
    if (params.store_numbers) queryParams.store_numbers = params.store_numbers;
    if (params.account_types) queryParams.account_types = params.account_types;
    if (params.report_categories) queryParams.report_categories = params.report_categories;

    const response = await client.get("/stores/store-attribution", {
      params: queryParams,
    });
    return response.data;
  },

  async getAllStoresTotal(params: GetAllStoresTotalParams) {
    // Convert array parameters to comma-separated strings
    const queryParams: Record<string, any> = {};

    // Only include parameters that have values
    if (params.store_ids) queryParams.store_ids = params.store_ids;
    if (params.store_names) queryParams.store_names = params.store_names;
    if (params.chain_names) queryParams.chain_names = params.chain_names;
    if (params.territory_managers) queryParams.territory_managers = params.territory_managers;
    if (params.district_managers) queryParams.district_managers = params.district_managers;
    if (params.store_numbers) queryParams.store_numbers = params.store_numbers;
    if (params.account_types) queryParams.account_types = params.account_types;
    if (params.report_categories) queryParams.report_categories = params.report_categories;

    // Add pagination and count parameters
    if (params.count_only !== undefined) queryParams.count_only = params.count_only;
    if (params.limit !== undefined) queryParams.limit = params.limit;
    if (params.offset !== undefined) queryParams.offset = params.offset;

    const response = await client.get("/stores/store-attribution/all", {
      params: queryParams,
      // Increase timeout for large exports
      timeout: 300000, // 5 minutes
    });
    return response.data;
  },
  async getStoreFilterValues(field: string, search?: string, limit?: number, offset?: number) {
    const response = await client.get("/stores/store-attribution/filter-values", {
      params: { field, search, limit, offset },
    });
    return response.data;
  },

  async updateStore(nutro_store_id: number, updateData: StoreUpdateRequest) {
    const response = await client.put(`/stores/store-attribution/${nutro_store_id}`, updateData);
    return response.data;
  },

  async deleteStores(deleteRequest: DeleteRequest) {
    const response = await client.delete("/stores/store-attribution/", {
      data: deleteRequest,
    });
    return response.data;
  },
  async findStores(params: FindStoreParams) {
    const response = await client.get<FindStoreResponse>("/stores/find-store", {
      params,
    });
    return {
      results: response.data.results,
      total_count: response.data.total_count,
    };
  },
};

export default StoreService;
