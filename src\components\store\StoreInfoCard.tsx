import React, { useState } from "react";
import { Box, Paper, Typography, Divider, Grid, IconButton } from "@mui/material";
import styles from "../../styles/StoreInfoCard.module.css";
import EditStoreModal from "./EditStoreModal";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";

interface StoreInfo {
  storeId: string;
  description: string;
  dataSource: string;
  address: string;
  firstSeen: string;
  lastUpdated: string;
  status: string;
  phone: string;
  rep: string;
  customerType: string;
  warehouse: string;
  country: string;
  last4WeekPOS: string;
  last52WeekPOS: string;
  total2010On: string;
  isPending: boolean;
}

interface StoreInfoCardProps {
  storeInfo: StoreInfo;
  onUnmap: () => void;
  isPending: boolean;
}

const StoreInfoCard: React.FC<StoreInfoCardProps> = ({ storeInfo, onUnmap, isPending }) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  return (
    <>
      <Paper elevation={3} className={styles.card}>
        <Box sx={{ display: "flex", justifyContent: "space-between" }}>
          <Typography variant="h6" className={styles.title}>
            {storeInfo.storeId} - {storeInfo.description}
          </Typography>
          <Box>
            <IconButton onClick={() => setIsEditModalOpen(true)}>
              <EditIcon color="primary" />
            </IconButton>
            <IconButton onClick={onUnmap} disabled={isPending || !storeInfo.status}>
              <DeleteIcon color={storeInfo.status ? "error" : "disabled"} />
            </IconButton>
          </Box>
        </Box>
        <Divider className={styles.divider} />

        {/* First two items in single line */}
        <Box sx={{ display: "flex", gap: 4, mb: 2 }}>
          <Box className={styles.infoSection}>
            <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
              Data Source
            </Typography>
            <Typography variant="body1">{storeInfo.dataSource}</Typography>
          </Box>
          <Box className={styles.infoSection}>
            <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
              Address
            </Typography>
            <Typography variant="body1">{storeInfo.address}</Typography>
          </Box>
        </Box>

        {/* Remaining items in 2-column grid */}
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                Phone
              </Typography>
              <Typography variant="body1">{storeInfo.phone}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                Representative
              </Typography>
              <Typography variant="body1">{storeInfo.rep}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                First Transaction
              </Typography>
              <Typography variant="body1">{storeInfo.firstSeen}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                Last Transaction
              </Typography>
              <Typography variant="body1">{storeInfo.lastUpdated}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                Customer Type
              </Typography>
              <Typography variant="body1">{storeInfo.customerType}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                Warehouse
              </Typography>
              <Typography variant="body1">{storeInfo.warehouse}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                Country
              </Typography>
              <Typography variant="body1">{storeInfo.country}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                Last 4 Week POS
              </Typography>
              <Typography variant="body1">{storeInfo.last4WeekPOS}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                Last 52 Week POS
              </Typography>
              <Typography variant="body1">{storeInfo.last52WeekPOS}</Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box className={styles.infoSection}>
              <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
                Total POS (2010 On)
              </Typography>
              <Typography variant="body1">{storeInfo.total2010On}</Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Status section with different background */}
        <Box
          sx={{
            mt: 2,
            p: 2,
            backgroundColor: "#f5f5f5",
            borderRadius: 1,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box>
            <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
              Status
            </Typography>
            <Typography variant="body1">{storeInfo.status || "No status"}</Typography>
          </Box>
          <Box>
            <Typography variant="subtitle2" sx={{ color: "primary.main" }}>
              Pending Status
            </Typography>
            <Typography variant="body1">{storeInfo.isPending ? "Yes" : "No"}</Typography>
          </Box>
        </Box>
      </Paper>

      <EditStoreModal
        open={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        storeInfo={storeInfo}
      />
    </>
  );
};

export default StoreInfoCard;
